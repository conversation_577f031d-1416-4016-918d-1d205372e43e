# Finger Frenzy

A fast-paced tapping game built with Phaser 3 and TypeScript. Test your reflexes by tapping green squares while avoiding purple ones!

## Game Mechanics

### Objective
Tap as many green squares as possible within the 60-second time limit while avoiding purple squares to achieve the highest score.

### Gameplay
- **Green Squares**: Tap these to earn points. Consecutive green taps increase your point meter.
- **Purple Squares**: Avoid these! Tapping purple squares deducts 5 points and resets your consecutive green taps.
- **Point Meter**: A visual indicator on the right side of the screen that increases with green taps and slowly decays over time. Higher meter values award more points per tap:
  - 0-20%: 1 point
  - 20-40%: 2 points
  - 40-60%: 3 points
  - 60-80%: 4 points
  - 80-100%: 5 points
- **Shuffle**: After a random number of consecutive green taps (2-7), the grid shuffles to increase difficulty.
- **Game Duration**: 60 seconds with a countdown at the start.

### End Game
When time runs out, the game displays:
- Final score
- Number of green and purple taps
- Accuracy percentage (green taps / total taps)

## Project Setup

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation
1. Clone the repository
2. Install dependencies:
```bash
npm install
```

### Development
Run the development server:
```bash
npm start
```
This will start the game on http://localhost:9000

### Build
Create a production build:
```bash
npm run build
```
The built files will be in the `dist` directory.

## Project Structure

```
finger_frenzy/
├── src/                  # Source code
│   ├── assets/           # Game assets (images, audio)
│   ├── objects/          # Game object classes
│   │   └── Square.ts     # Square game object
│   ├── scenes/           # Game scenes
│   │   ├── GameScene.ts  # Main gameplay scene
│   │   ├── PreloadScene.ts # Asset loading scene
│   │   └── GameStartScene.ts # Title screen scene
│   ├── utils/            # Utility classes
│   │   └── TicTapsConnector.ts # Integration with TicTaps platform
│   ├── index.html        # HTML template
│   └── index.ts          # Main entry point
├── package.json          # Project dependencies and scripts
├── tsconfig.json         # TypeScript configuration
└── webpack.config.js     # Webpack configuration
```

## Code Style Guidelines

The project follows these code style practices:

- **TypeScript**: Uses strict mode with ES2015 target
- **Classes**: Each game object and scene is implemented as a class
- **Naming Conventions**:
  - PascalCase for class names (e.g., `GameScene`, `Square`)
  - camelCase for variables and methods
  - UPPERCASE for enum values
- **File Organization**: 
  - One class per file
  - Files organized by functionality (scenes, objects, utils)
- **Comments**: Important methods and complex logic are documented with comments

## Development Notes

- The game is designed to work on both desktop and mobile devices
- Supports multi-touch with up to 3 active pointers
- Uses Phaser's arcade physics system
- Integrates with the TicTaps platform for score tracking

  
