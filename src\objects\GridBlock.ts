import Phaser from 'phaser';
import GameConfig from '../config/GameConfig';

export default class GridBlock extends Phaser.GameObjects.Container {
  private blockActiveImage: Phaser.GameObjects.Image;
  private blockInactiveImage: Phaser.GameObjects.Image;
  private isBlockActive: boolean = false;
  private activationTime: number = 0;
  private row: number;
  private col: number;
  public isShowingWrongState: boolean = false;

  constructor(scene: Phaser.Scene, x: number, y: number, width: number, height: number, row: number, col: number) {
    super(scene, x, y);

    this.row = row;
    this.col = col;

    // Create inactive block image
    this.blockInactiveImage = scene.add.image(0, 0, GameConfig.ASSETS.IMAGES.BLOCK_INACTIVE);
    this.blockInactiveImage.setDisplaySize(width, height);
    this.blockInactiveImage.setOrigin(0.5, 0.5);
    this.add(this.blockInactiveImage);

    // Create active block image
    this.blockActiveImage = scene.add.image(0, 0, GameConfig.ASSETS.IMAGES.BLOCK_ACTIVE);
    this.blockActiveImage.setDisplaySize(width * 0.6, height * 0.85);
    this.blockActiveImage.setOrigin(0.5, 0.5);
    this.blockActiveImage.setVisible(false);
    this.add(this.blockActiveImage);

    // Add to scene and make interactive
    scene.add.existing(this);
    this.setSize(width, height);
    this.setInteractive({ useHandCursor: true });
  }

  public getBlockActive(): boolean {
    return this.isBlockActive;
  }

  public getActivationTime(): number {
    return this.activationTime;
  }


  public setBlockActive(active: boolean): void {
    if (this.isBlockActive === active) return;
    
    this.isBlockActive = active;
    this.blockActiveImage.setVisible(active);
    this.blockInactiveImage.setVisible(!active);

    if (active) {
      this.activationTime = Date.now();
      
      // Simple pop animation
      this.blockActiveImage.setScale(0.95);
      this.scene.tweens.add({
        targets: this.blockActiveImage,
        scale: 1,
        duration: GameConfig.BLOCK_ANIMATION_DURATION,
        ease: 'Back.easeOut'
      });
    }
  }

  public setBlockWrong(): void {
    if (this.isShowingWrongState) return; // Prevent multiple wrong animations
    
    this.isShowingWrongState = true;
    const image = this.isBlockActive ? this.blockActiveImage : this.blockInactiveImage;
    image.setTint(0xff4444);
    
    this.scene.time.delayedCall(400, () => {
      image.setTint(0xffffff);
      this.isShowingWrongState = false;
    });
  }

  public reset(): void {
    this.isBlockActive = false;
    this.isShowingWrongState = false;
    this.blockActiveImage.setVisible(false);
    this.blockActiveImage.setTint(0xffffff);
    this.blockActiveImage.setScale(1);
    this.blockInactiveImage.setVisible(true);
    this.blockInactiveImage.setTint(0xffffff);
  }
}
