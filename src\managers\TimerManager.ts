import Phaser from 'phaser';
import { TimerConfig, TimerState } from '../types/types';

/**
 * ```
 * const timer = new TimerManager(scene, {
 *   duration: 30,
 *   warningThreshold: 5
 * });
 * 
 * timer.createUI(x, y);
 * timer.start();
 * timer.on('timeUp', () => console.log('Time is up!'));
 * ```
 */
export default class TimerManager {
  private scene: Phaser.Scene;
  private config: Required<TimerConfig>;
  
  // Timer state
  private startTime: number = 0;
  private isRunning: boolean = false;
  private isFinished: boolean = false;
  
  // UI Elements
  private timeText?: Phaser.GameObjects.Text;
  private container?: Phaser.GameObjects.Container;
  
  // Timer event
  private timerEvent?: Phaser.Time.TimerEvent;
  
  // Events
  private events: Phaser.Events.EventEmitter;

  constructor(scene: Phaser.Scene, config: TimerConfig) {
    this.scene = scene;
    this.events = new Phaser.Events.EventEmitter();
    
    // Set default configuration
    this.config = {
      duration: config.duration,
      updateInterval: config.updateInterval ?? 100,
      fontFamily: config.fontFamily ?? 'Arial',
      fontSize: config.fontSize ?? '24px',
      normalColor: config.normalColor ?? '#FFFFFF',
      warningColor: config.warningColor ?? '#ff0000',
      warningThreshold: config.warningThreshold ?? 5
    };
  }

  /**
   * Create the timer UI at the specified position
   */
  public createUI(x: number, y: number, parentContainer?: Phaser.GameObjects.Container): void {
    // Create container for timer elements
    this.container = this.scene.add.container(0, 0);
    
    // Create time text
    this.timeText = this.scene.add.text(x, y, this.formatTime(this.config.duration), {
      fontFamily: this.config.fontFamily,
      fontSize: this.config.fontSize,
      fontStyle: 'bold',
      color: this.config.normalColor
    }).setOrigin(0.5);
    
    // Add to container
    this.container.add(this.timeText);
    
    // Add to parent container if provided
    if (parentContainer) {
      parentContainer.add(this.container);
    }
  }

  /**
   * Start the countdown timer
   */
  public start(): void {
    if (this.isRunning) return;

    // Fresh start
    this.startTime = this.scene.time.now;
    this.isFinished = false;
    this.isRunning = true;

    // Clear existing timer event
    if (this.timerEvent) {
      this.timerEvent.destroy();
    }

    // Create timer event for updates
    this.timerEvent = this.scene.time.addEvent({
      delay: this.config.updateInterval,
      callback: this.updateTimer.bind(this),
      loop: true
    });

    this.events.emit('timerStarted');
  }

  /**
   * Stop and reset the timer
   */
  public stop(): void {
    // this.isRunning = false;
    // this.isFinished = false;
    // this.startTime = 0;

    if (this.timerEvent) {
      this.timerEvent.destroy();
      this.timerEvent = undefined;
    }

    // // Reset display
    // if (this.timeText) {
    //   this.timeText.setText(this.formatTime(this.config.duration));
    //   this.timeText.setColor(this.config.normalColor);
    // }

    this.events.emit('timerStopped');
  }

  /**
   * Get current timer state
   */
  public getState(): TimerState {
    const timeRemaining = this.getTimeRemaining();
    const progress = timeRemaining / this.config.duration;

    return {
      timeRemaining,
      totalDuration: this.config.duration,
      isRunning: this.isRunning,
      isFinished: this.isFinished,
      progress: Math.max(0, Math.min(1, progress))
    };
  }

  /**
   * Get time remaining in seconds
   */
  public getTimeRemaining(): number {
    if (!this.isRunning) return this.config.duration;

    const elapsed = this.scene.time.now - this.startTime;
    return Math.max(0, this.config.duration - (elapsed / 1000));
  }

  /**
   * Update timer display and check for completion
   */
  private updateTimer(): void {
    const timeRemaining = this.getTimeRemaining();
    const seconds = Math.ceil(timeRemaining);
    
    // Update display
    if (this.timeText) {
      this.timeText.setText(this.formatTime(seconds));
      
      // Change color if in warning threshold
      const color = seconds <= this.config.warningThreshold 
        ? this.config.warningColor 
        : this.config.normalColor;
      this.timeText.setColor(color);
    }
    
    // Emit progress event
    const state = this.getState();
    this.events.emit('timerUpdate', state);
    
    // Check for completion
    if (timeRemaining <= 0 && !this.isFinished) {
      this.isFinished = true;
      this.isRunning = false;
      
      if (this.timerEvent) {
        this.timerEvent.destroy();
        this.timerEvent = undefined;
      }
      
      this.events.emit('timeUp');
    }
  }

  /**
   * Format time in seconds to display string
   */
  private formatTime(seconds: number): string {
    return `${Math.max(0, Math.floor(seconds))}s`;
  }
  
  /**
   * Subscribe to timer events
   */
  public on(event: 'timerStarted' | 'timerStopped' | 'timerUpdate' | 'timeUp',
           callback: (...args: any[]) => void): void {
    this.events.on(event, callback);
  }

  /**
   * Unsubscribe from timer events
   */
  public off(event: 'timerStarted' | 'timerStopped' | 'timerUpdate' | 'timeUp',
            callback: (...args: any[]) => void): void {
    this.events.off(event, callback);
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    this.stop();
    this.events.removeAllListeners();
    
    if (this.container) {
      this.container.destroy();
    }
    
    this.timeText = undefined;
    this.container = undefined;
  }
}