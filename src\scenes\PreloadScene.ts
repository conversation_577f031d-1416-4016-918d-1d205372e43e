import Phaser from 'phaser';
import GameConfig from '../config/GameConfig';

export default class PreloadScene extends Phaser.Scene {
  constructor() {
    super({ key: 'PreloadScene' });
  }

  preload(): void {
    const { width, height } = this.cameras.main;

    // Create simple loading bar
    const progressBar = this.add.graphics();
    const progressBox = this.add.graphics();
    progressBox.fillStyle(0x222222, 0.8);
    progressBox.fillRect(width / 4, height / 2 - 25, width / 2, 50);

    const loadingText = this.add.text(width / 2, height / 2 + 50, 'Loading...', {
      fontSize: '24px',
      color: '#ffffff'
    }).setOrigin(0.5);

    // Update loading bar
    this.load.on('progress', (value: number) => {
      progressBar.clear();
      progressBar.fillStyle(0x00aaff, 1);
      progressBar.fillRect(width / 4, height / 2 - 25, (width / 2) * value, 50);
      loadingText.setText(`Loading... ${Math.floor(value * 100)}%`);
    });

    // Load images manually line by line
    this.load.image('block_active', 'assets/images/block_active.png');
    this.load.image('block_inactive', 'assets/images/block_inactive.png');
    this.load.image('game_start', 'assets/images/game_start.png');
    this.load.image('game_name', 'assets/images/game_name.png');
    this.load.image('timer_icon', 'assets/images/timer_icon.png');
    this.load.image('countdown-3', 'assets/images/countdown-3.png');
    this.load.image('countdown-2', 'assets/images/countdown-2.png');
    this.load.image('countdown-1', 'assets/images/countdown-1.png');
    this.load.image('countdown-go', 'assets/images/countdown-go.png');
    this.load.image('back_to_lobby', 'assets/images/back_to_lobby.png');
    this.load.image('game_bg', 'assets/images/game_bg.png');
    
    // Load SVG assets
    this.load.svg('button_bg', 'assets/images/button_bg.svg');
    this.load.svg('game_over', 'assets/images/game_over.svg');
    this.load.svg('timer_bg', 'assets/images/timer_bg.svg');
    this.load.image('timer_countdown_bg', 'assets/images/timer_countdown_bg.png');

    // Load sounds manually line by line
    this.load.audio('tap', ['assets/sounds/tap.ogg', 'assets/sounds/tap.mp3', 'assets/sounds/tap.wav']);
    this.load.audio('right', ['assets/sounds/right.ogg', 'assets/sounds/right.mp3', 'assets/sounds/right.wav']);
    this.load.audio('wrong', ['assets/sounds/wrong.ogg', 'assets/sounds/wrong.mp3', 'assets/sounds/wrong.wav']);
    this.load.audio('timeout', ['assets/sounds/timeout.ogg', 'assets/sounds/timeout.mp3', 'assets/sounds/timeout.wav']);
    this.load.audio('click', ['assets/sounds/click.ogg', 'assets/sounds/click.mp3', 'assets/sounds/click.wav']);
    this.load.audio('countdown', ['assets/sounds/countdown.ogg', 'assets/sounds/countdown.mp3', 'assets/sounds/countdown.wav']);
    this.load.audio('go', ['assets/sounds/go.mp3', 'assets/sounds/go.wav']);
  }

  create(): void {
    // Go to start scene when loading complete
    this.scene.start('GameStartScene');
  }
}
