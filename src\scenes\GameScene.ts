import Phaser from 'phaser';
import ScoreManager from '../managers/ScoreManager';
import TimerManager from '../managers/TimerManager';
import TimerBarUI from '../ui/TimerBarUI';
import GridBlock from '../objects/GridBlock';
import GameConfig from '../config/GameConfig';
import TicTapsConnector from '../utils/TicTapsConnector';

export default class GameScene extends Phaser.Scene {
  private blocks: GridBlock[] = [];
  
  private timeLeft: number = GameConfig.GAME_DURATION;
  private gameTimer: Phaser.Time.TimerEvent | null = null;
  private gameEnd: boolean = false;
  private gameStartTime: number = 0; // Track when game actually started

  // UI Elements
  private UIContainer!: Phaser.GameObjects.Container;
  private gridContainer!: Phaser.GameObjects.Container;

  private scoreManager!: ScoreManager;
  private timerManager!: TimerManager;
  private timerBarUI!: TimerBarUI;

  // TicTaps integration
  private ticTaps: TicTapsConnector;

  constructor() {
    super({ key: 'GameScene' });
    this.ticTaps = new TicTapsConnector();
  }

  init(): void {
    // Reset game state
    this.timeLeft = GameConfig.GAME_DURATION;
    this.gameEnd = false;
    this.gameStartTime = 0;

    // Initialize managers
    this.scoreManager = new ScoreManager(this, {
      initialScore: 0,
      fontSize: '80px',
      scoreColor: '#33DDFF'
    });

    this.timerManager = new TimerManager(this, {
      duration: GameConfig.GAME_DURATION,
      warningThreshold: 5
    });

    this.timerBarUI = new TimerBarUI(this, {
      width: this.cameras.main.width * 0.8,
      height: 35,
      x: this.cameras.main.width / 2,
      y: this.cameras.main.height * 0.07
    });

    // Set up timer events
    this.timerManager.on('timeUp', () => this.endGame());
    this.timerManager.on('timerUpdate', (state) => {
      this.timerBarUI.updateProgress(state.progress);
    });
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Simple gradient background
    this.createBackground();
    
    // Start countdown immediately
    this.startCountdown();

    this.UIContainer = this.add.container(0, 0);
    // this.gamePanel.add(timerContainer);

    // Create timer bar UI
    this.timerBarUI.create(this.UIContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);

    // Create score display
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 110, this.UIContainer);

  }

  private createBackground(): void {
    const { width, height } = this.cameras.main;
    
    // Create gradient background
    const bgTexture = this.textures.createCanvas('bgTexture', width, height);
    const bgContext = bgTexture?.getContext();
    
    if (bgContext && bgTexture) {
      const gradient = bgContext.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, '#212429');
      gradient.addColorStop(1, '#1C1D22');
      
      bgContext.fillStyle = gradient;
      bgContext.fillRect(0, 0, width, height);
      bgTexture.refresh();
      
      this.add.image(width / 2, height / 2, 'bgTexture').setOrigin(0.5);
    } else {
      // Fallback solid color
      this.cameras.main.setBackgroundColor('#1C1D22');
    }
  }

  private async startCountdown(): Promise<void> {
    const { width, height } = this.cameras.main;
    const countdownImages = ['countdown-3', 'countdown-2', 'countdown-1', 'countdown-go'];
    
    // Create countdown overlay
    const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
    const countdownImage = this.add.image(width / 2, height / 2, 'countdown-3').setScale(0);

    // Animate countdown
    for (const texture of countdownImages) {
      countdownImage.setTexture(texture);
      
      // Play sound
      try {
        this.sound.play(texture === 'countdown-go' ? 'go' : 'countdown');
      } catch (error) {
        console.warn('Sound playback failed:', error);
      }

      // Animate scale
      await new Promise<void>((resolve) => {
        this.tweens.add({
          targets: countdownImage,
          scale: 0.2,
          duration: 300,
          ease: 'Back.easeOut',
          onComplete: () => {
            this.time.delayedCall(700, () => {
              this.tweens.add({
                targets: countdownImage,
                scale: 0,
                duration: 300,
                ease: 'Back.easeIn',
                onComplete: () => resolve()
              });
            });
          }
        });
      });
    }

    // Remove countdown elements
    overlay.destroy();
    countdownImage.destroy();

    // Create game UI and start
    this.createUI();
    this.createGrid();
    this.startGame();
  }

  private createUI(): void {
    const { width, height } = this.cameras.main;

    this.UIContainer = this.add.container(0, 0);
    // this.gamePanel.add(timerContainer);

    // Create timer bar UI
    this.timerBarUI.create(this.UIContainer);

    // Create timer text at the right circle position
    const rightCirclePos = this.timerBarUI.getRightCirclePosition();
    this.timerManager.createUI(rightCirclePos.x, rightCirclePos.y, this.UIContainer);

    // Create score display
    const timerBarY = height * 0.07;
    this.scoreManager.createUI(width / 2, timerBarY + 110, this.UIContainer);
  }

  private createGrid(): void {
    const { width, height } = this.cameras.main;
    
    // Container setup
    const containerWidth = Math.min(width * 0.88, 550);
    const containerHeight = containerWidth * 1.3;
    
    this.gridContainer = this.add.container(width / 2, height * 0.6);
    
    // Background
    const bg = this.add.graphics();
    bg.fillStyle(0x1a1a1a, 1);
    bg.fillRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    bg.lineStyle(4, 0x4579F5, 1);
    bg.strokeRoundedRect(-containerWidth / 2, -containerHeight / 2, containerWidth, containerHeight, 20);
    this.gridContainer.add(bg);

    // Create 4x4 grid
    this.blocks = [];
    const gap = containerWidth * 0.03;
    const cellWidth = (containerWidth - (gap * 5)) / 4;
    const cellHeight = (containerHeight - (gap * 5)) / 4;
    const startX = -containerWidth / 2 + gap;
    const startY = -containerHeight / 2 + gap;

    for (let row = 0; row < 4; row++) {
      for (let col = 0; col < 4; col++) {
        const x = startX + col * (cellWidth + gap) + cellWidth / 2;
        const y = startY + row * (cellHeight + gap) + cellHeight / 2;
        
        const block = new GridBlock(this, x, y, cellWidth, cellHeight, row, col);
        this.blocks.push(block);
        this.gridContainer.add(block);
        
        block.on('pointerdown', (pointer: Phaser.Input.Pointer) => this.onBlockClick(block, pointer));
      }
    }
  }

  private onBlockClick(block: GridBlock, pointer: Phaser.Input.Pointer): void {
    if (this.gameEnd || !block) return;
    
    // Check if this is a fresh click (not a held touch)
    // pointer.getDuration() returns how long the pointer has been down
    if (pointer.getDuration() > 100) {
      return; // Ignore held touches
    }
    
    // Disable the block's input immediately to prevent same-touch duplicate events
    block.disableInteractive();
    
    const blockIndex = this.blocks.indexOf(block);
    const isActive = block.getBlockActive();
    
    if (isActive) {
      // Active block clicked - add points
      this.sound.play('right');
      
      // Calculate points based on reaction time
      const currentTime = Date.now();
      const reactionTime = currentTime - block.getActivationTime();
      let points = 1;
      if (reactionTime < 500) points = 5;
      else if (reactionTime < 1000) points = 4;
      else if (reactionTime < 1500) points = 3;
      else if (reactionTime < 2000) points = 2;

      // Update score
      // this.score += points;
      // this.scoreText.setText(this.score.toString());
      this.scoreManager.addPoints(points);

      // Show score animation
      this.showScoreText(this.gridContainer.x + block.x, this.gridContainer.y + block.y, `+${points}`);

      // Deactivate block and activate a new one
      block.setBlockActive(false);
      
      // Get block index to exclude from new activation
      const clickedBlockIndex = this.blocks.indexOf(block);
      this.activateRandomBlock(clickedBlockIndex);
      
    } else {
      // Inactive block clicked - lose points
      this.sound.play('wrong');
      
      // Show wrong state and penalty
      block.setBlockWrong();
      this.score = Math.max(0, this.score - 5);
      this.scoreText.setText(this.score.toString());

      this.scoreManager.addPoints(points);
      
      this.showScoreText(this.gridContainer.x + block.x, this.gridContainer.y + block.y, '-5');
    }
    
    // Re-enable interaction after a short delay to allow the touch to complete
    this.time.delayedCall(100, () => {
      block.setInteractive({ useHandCursor: true });
    });
  }

  private showScoreText(x: number, y: number, text: string): void {
    const scoreText = this.add.text(x, y, text, {
      fontSize: '36px',
      fontFamily: 'Arial',
      color: text.startsWith('+') ? '#ffff00' : '#ff0000',
      stroke: '#000000',
      strokeThickness: 2
    }).setOrigin(0.5);

    this.tweens.add({
      targets: scoreText,
      y: y - 40,
      alpha: 0,
      duration: 400,
      ease: 'Linear',
      onComplete: () => scoreText.destroy()
    });
  }




  private activateRandomBlock(excludeBlockIndex?: number): void {
    if (this.gameEnd) return;
    
    const currentActiveCount = this.blocks.filter(b => b.getBlockActive()).length;
    if (currentActiveCount >= GameConfig.INITIAL_ACTIVE_BLOCKS) return;

    // Get list of available positions (exclude currently active blocks and the clicked block)
    const availablePositions: number[] = [];
    
    this.blocks.forEach((block, index) => {
      const isActive = block.getBlockActive();
      const isExcluded = excludeBlockIndex !== undefined && index === excludeBlockIndex;
      
      if (!isActive && !isExcluded) {
        availablePositions.push(index);
      }
    });
    
    // If we have available positions, activate one randomly
    if (availablePositions.length > 0) {
      const randomIndex = Phaser.Utils.Array.GetRandom(availablePositions);
      this.blocks[randomIndex].setBlockActive(true);
    }
  }

  private startGame(): void {
    // Reset all blocks
    this.blocks.forEach(block => block.reset());
    
    // Activate initial blocks
    const positions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];

    for (const pos of positions) {
      const index = pos.row * 4 + pos.col;
      if (index < this.blocks.length) {
        this.blocks[index].setBlockActive(true);
      }
    }
    
    console.log('Game started with 3 active blocks');

    // Record game start time
    this.gameStartTime = this.time.now;
    
    // Cache timer bar dimensions for performance
    const { width, height } = this.cameras.main;
    this.timerBarWidth = (width * 0.8) - 4;
    this.timerBarX = width / 2;
    this.timerBarY = height * 0.07;
    
    // Start game timer - update every 100ms (10fps) which is sufficient for UI
    this.gameTimer = this.time.addEvent({
      delay: 100,
      callback: this.updateTimer,
      callbackScope: this,
      loop: true
    });
  }

  private updateTimer(): void {
    // Calculate time remaining based on elapsed time
    const elapsedTime = this.time.now - this.gameStartTime;
    const timeLeft = Math.max(0, GameConfig.GAME_DURATION - elapsedTime / 1000);
    
    // Update text when seconds change
    const currentSecond = Math.ceil(timeLeft);
    if (currentSecond !== this.timeLeft) {
      this.timeLeft = currentSecond;
      this.timeText.setText(`${this.timeLeft}s`);
      
      // Change color when time is low (only check when seconds change)
      if (this.timeLeft <= 5) {
        this.timeText.setColor('#ff0000');
      }
    }
    
    // Update timer bar using cached values
    if (this.timerMask) {
      const percentage = timeLeft / GameConfig.GAME_DURATION;
      const visibleWidth = this.timerBarWidth * percentage;
      
      this.timerMask.clear();
      this.timerMask.fillRect(
        this.timerBarX - this.timerBarWidth / 2, 
        this.timerBarY - 15.5, 
        visibleWidth, 
        31
      );
    }

    if (timeLeft <= 0) {
      this.endGame();
    }
  }

  private endGame(): void {
    if (this.gameEnd) return;
    
    this.gameEnd = true;
    this.sound.play('timeout');
    
    // Immediately disable all blocks to prevent further interaction
    this.blocks.forEach(block => block.disableInteractive());
    
    if (this.gameTimer) {
      this.gameTimer.remove();
      this.gameTimer = null;
    }

    const gameElapsed = (this.time.now - this.gameStartTime) / 1000;
    console.log(`Game ended - Final score: ${this.score}, Duration: ${gameElapsed.toFixed(1)}s`);

    // Transition to end scene after a short delay (matching Matching Mayhem pattern)
    this.time.delayedCall(500, () => {
      // Clean up the scene before transitioning
      this.blocks.forEach(block => block.destroy());
      this.blocks = [];
      
      // Stop all tweens
      this.tweens.killAll();
      
      console.log('Transitioning to GameEndScene with score:', this.score);
      this.scene.start('GameEndScene', { score: this.score });
    });
  }

  shutdown(): void {
    if (this.gameTimer) {
      this.gameTimer.remove();
      this.gameTimer = null;
    }
    this.tweens.killAll();

    // Clean up managers
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }

    if (this.timerManager) {
      this.timerManager.destroy();
    }

    if (this.timerBarUI) {
      this.timerBarUI.destroy();
    }
  }
}
