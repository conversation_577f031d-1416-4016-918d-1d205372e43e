# Finger Frenzy Phaser Game Module Refactoring Task List

This document outlines the tasks required to refactor the Finger Frenzy Phaser game from an iframe-based implementation to a module-based implementation that can be loaded directly into a React component.

## Prerequisites
- The game currently runs as a standalone HTML page loaded in an iframe
- Uses webpack to bundle everything into a single HTML file with embedded JS
- Communicates with parent via postMessage
- Needs to become a JavaScript module that exports a class/function to initialize the game

---

## 1. Module Architecture Refactoring

### 1.1 Create Module Export Structure
**Complexity:** Medium  
**File:** `src/index.ts`  
**Dependencies:** None  

Create a module that exports a class or function to initialize the game instead of auto-initializing on DOM load.

```typescript
// src/index.ts
export interface FingerFrenzyGameConfig {
  container: HTMLElement;
  onReady?: () => void;
  onScore?: (score: number) => void;
  onQuit?: () => void;
  debug?: boolean;
}

export class FingerFrenzyGame {
  private game: Phaser.Game | null = null;
  private config: FingerFrenzyGameConfig;
  
  constructor(config: FingerFrenzyGameConfig) {
    this.config = config;
  }
  
  init(): void {
    // Move initGame logic here
  }
  
  destroy(): void {
    if (this.game) {
      this.game.destroy(true);
      this.game = null;
    }
  }
}

// No auto-initialization - remove DOM ready check
```

**Warning:** This breaks the current standalone functionality. Consider maintaining both modes during transition.

### 1.2 Update Entry Point Configuration
**Complexity:** Easy  
**File:** `webpack.config.js`  
**Dependencies:** Task 1.1  

Update webpack to build as a library instead of a standalone app.

```javascript
module.exports = {
  entry: './src/index.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'finger-frenzy.js',
    library: {
      name: 'FingerFrenzy',
      type: 'umd',
      export: 'default'
    },
    clean: true
  },
  // ... rest of config
};
```

### 1.3 Remove HTML Template
**Complexity:** Easy  
**File:** `webpack.config.js`, `src/index.html`  
**Dependencies:** Task 1.2  

Remove HtmlWebpackPlugin since we won't need the HTML wrapper anymore.

```javascript
// Remove from webpack.config.js:
// new HtmlWebpackPlugin({
//   template: './src/index.html',
// }),
```

**Consider:** Keep the HTML file for development/testing purposes but don't include in build.

---

## 2. Asset Loading Changes

### 2.1 Convert Asset Paths to Dynamic Loading
**Complexity:** Hard  
**Files:** `src/scenes/PreloadScene.ts`, `src/config/GameConfig.ts`  
**Dependencies:** None  

Assets are currently loaded with relative paths. Need to make paths configurable.

```typescript
// src/config/GameConfig.ts
export interface AssetConfig {
  baseUrl?: string;
  basePath?: string;
}

// In PreloadScene.ts
preload(): void {
  const baseUrl = this.game.config.assetBaseUrl || '';
  
  // Update all asset loads
  this.load.image('block', `${baseUrl}/assets/images/block.png`);
  // ... etc
}
```

**Warning:** This affects ALL asset loading throughout the game. Test thoroughly.

### 2.2 Create Asset Manifest
**Complexity:** Medium  
**File:** Create `src/assets/manifest.json`  
**Dependencies:** Task 2.1  

Create a manifest of all required assets for easier management.

```json
{
  "images": [
    { "key": "block", "path": "assets/images/block.png" },
    { "key": "background", "path": "assets/images/background.png" }
  ],
  "audio": [
    { "key": "tap", "path": "assets/audio/tap.mp3" },
    { "key": "wrong", "path": "assets/audio/wrong.mp3" }
  ]
}
```

### 2.3 Implement Asset Preloading Strategy
**Complexity:** Medium  
**Files:** `src/scenes/PreloadScene.ts`, `src/index.ts`  
**Dependencies:** Tasks 2.1, 2.2  

Allow host application to preload assets or provide asset URLs.

```typescript
export interface FingerFrenzyGameConfig {
  // ... existing config
  assets?: {
    baseUrl?: string;
    manifest?: AssetManifest;
    preloaded?: Map<string, any>;
  };
}
```

---

## 3. Communication System Refactor

### 3.1 Replace PostMessage with Direct Callbacks
**Complexity:** Medium  
**File:** `src/utils/TicTapsConnector.ts`  
**Dependencies:** Task 1.1  

Convert from postMessage communication to direct callback invocation.

```typescript
// src/utils/TicTapsConnector.ts
export default class TicTapsConnector {
  private callbacks: {
    onReady?: () => void;
    onScore?: (score: number) => void;
    onQuit?: () => void;
  };

  constructor(callbacks: typeof this.callbacks) {
    this.callbacks = callbacks;
  }

  notifyGameReady(): void {
    this.callbacks.onReady?.();
    console.log('Game ready notification sent');
  }

  sendScore(score: number): void {
    this.callbacks.onScore?.(score);
    console.log('Score sent:', score);
  }

  notifyGameQuit(): void {
    this.callbacks.onQuit?.();
    console.log('Game quit notification sent');
  }
}
```

### 3.2 Update Scene Communication
**Complexity:** Easy  
**Files:** All scene files in `src/scenes/`  
**Dependencies:** Task 3.1  

Update all scenes to use the new callback-based communication.

```typescript
// In GameScene.ts, GameEndScene.ts, etc.
// Pass callbacks through scene data or game registry
```

### 3.3 Add Event Emitter for Additional Events
**Complexity:** Medium  
**File:** Create `src/utils/EventEmitter.ts`  
**Dependencies:** Task 3.1  

For more complex communication needs, implement a proper event system.

```typescript
export class GameEventEmitter extends Phaser.Events.EventEmitter {
  static readonly GAME_READY = 'game:ready';
  static readonly SCORE_UPDATE = 'score:update';
  static readonly GAME_QUIT = 'game:quit';
  // ... more events
}
```

---

## 4. Build System Updates

### 4.1 Create Development Harness
**Complexity:** Medium  
**File:** Create `dev/index.html`, `dev/index.js`  
**Dependencies:** Tasks 1.1, 1.2  

Create a development environment to test the module.

```html
<!-- dev/index.html -->
<!DOCTYPE html>
<html>
<head>
  <title>Finger Frenzy Dev</title>
  <style>
    #game-container {
      width: 100vw;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div id="game-container"></div>
  <script src="../dist/finger-frenzy.js"></script>
  <script src="index.js"></script>
</body>
</html>
```

### 4.2 Add Build Scripts
**Complexity:** Easy  
**File:** `package.json`  
**Dependencies:** Task 4.1  

Add separate build scripts for module and development.

```json
{
  "scripts": {
    "dev": "webpack serve --mode development --config webpack.dev.js",
    "build": "webpack --mode production",
    "build:module": "webpack --mode production --config webpack.module.js",
    "test:integration": "serve dev"
  }
}
```

### 4.3 Create Module-Specific Webpack Config
**Complexity:** Medium  
**File:** Create `webpack.module.js`  
**Dependencies:** Tasks 1.2, 4.2  

Separate configuration for module builds.

```javascript
const baseConfig = require('./webpack.config.js');

module.exports = {
  ...baseConfig,
  externals: {
    'phaser': 'Phaser'
  },
  output: {
    ...baseConfig.output,
    filename: 'finger-frenzy.module.js',
    library: {
      type: 'module'
    }
  },
  experiments: {
    outputModule: true
  }
};
```

**Consider:** Whether to bundle Phaser or expect it as an external dependency.

---

## 5. Cleanup and Lifecycle Management

### 5.1 Implement Proper Game Cleanup
**Complexity:** Medium  
**Files:** `src/index.ts`, all scene files  
**Dependencies:** Task 1.1  

Ensure all resources are properly cleaned up when game is destroyed.

```typescript
// In FingerFrenzyGame class
destroy(): void {
  // Remove all event listeners
  this.removeEventListeners();
  
  // Destroy all scenes properly
  if (this.game) {
    this.game.scene.scenes.forEach(scene => {
      scene.events.removeAllListeners();
    });
    
    // Destroy game instance
    this.game.destroy(true);
    this.game = null;
  }
  
  // Clear any timers
  this.clearTimers();
}
```

### 5.2 Add Scene Lifecycle Management
**Complexity:** Medium  
**Files:** All scene files  
**Dependencies:** Task 5.1  

Implement proper cleanup in each scene.

```typescript
// Base scene class
export abstract class BaseGameScene extends Phaser.Scene {
  shutdown(): void {
    // Remove all event listeners
    this.events.removeAllListeners();
    
    // Destroy all game objects
    this.children.removeAll();
    
    // Clear any scene-specific resources
    this.cleanup();
  }
  
  abstract cleanup(): void;
}
```

### 5.3 Memory Leak Prevention
**Complexity:** Hard  
**Files:** All files using event listeners or timers  
**Dependencies:** Tasks 5.1, 5.2  

Audit entire codebase for potential memory leaks.

**Check for:**
- Event listeners not removed
- Timers not cleared
- Textures not destroyed
- Audio not stopped/destroyed
- Circular references

---

## 6. Testing and Validation

### 6.1 Create Integration Test Suite
**Complexity:** Medium  
**File:** Create `test/integration.spec.js`  
**Dependencies:** All previous tasks  

Test the module integration with a mock React environment.

```javascript
describe('FingerFrenzy Module Integration', () => {
  it('should initialize without errors', () => {
    const container = document.createElement('div');
    const game = new FingerFrenzy.FingerFrenzyGame({
      container,
      onReady: () => console.log('Ready'),
      onScore: (score) => console.log('Score:', score),
      onQuit: () => console.log('Quit')
    });
    
    game.init();
    expect(game).toBeDefined();
    
    game.destroy();
  });
});
```

### 6.2 Performance Testing
**Complexity:** Medium  
**File:** Create `test/performance.js`  
**Dependencies:** Task 6.1  

Test for memory leaks and performance issues.

```javascript
// Monitor memory usage
// Test rapid init/destroy cycles
// Check for orphaned event listeners
```

### 6.3 Create Migration Guide
**Complexity:** Easy  
**File:** Create `MIGRATION.md`  
**Dependencies:** All tasks  

Document how to migrate from iframe to module usage.

```markdown
# Migration Guide

## Before (iframe)
```html
<iframe src="path/to/finger-frenzy/index.html"></iframe>
```

## After (module)
```javascript
import { FingerFrenzyGame } from 'finger-frenzy';

const game = new FingerFrenzyGame({
  container: document.getElementById('game-container'),
  onScore: (score) => handleScore(score)
});

game.init();
```

---

## Task Execution Order

1. **Phase 1 - Core Refactoring** (Tasks 1.1-1.3)
2. **Phase 2 - Communication** (Tasks 3.1-3.3)
3. **Phase 3 - Build System** (Tasks 4.1-4.3)
4. **Phase 4 - Asset Loading** (Tasks 2.1-2.3)
5. **Phase 5 - Cleanup** (Tasks 5.1-5.3)
6. **Phase 6 - Testing** (Tasks 6.1-6.3)

## Critical Considerations

⚠️ **Breaking Changes:** This refactoring will break the current iframe implementation. Consider maintaining both versions during transition.

⚠️ **Asset Loading:** The biggest challenge will be handling assets when the game is loaded as a module. Consider CDN hosting or inline base64 encoding for critical assets.

⚠️ **Performance:** Module loading might affect initial load time. Consider lazy loading strategies.

⚠️ **Browser Compatibility:** Ensure the module format works in all target browsers.

⚠️ **React Integration:** Test thoroughly with React's strict mode and concurrent features.