<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <!-- Mobile viewport optimization for iFrame usage -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">

  <!-- Mobile touch optimizations -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="msapplication-tap-highlight" content="no">
  
  <!-- Disable unwanted mobile features -->
  <meta name="format-detection" content="telephone=no">
  <meta name="format-detection" content="date=no">
  <meta name="format-detection" content="address=no">
  <meta name="format-detection" content="email=no">
  
  <title>Finger Frenzy</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }

    html, body {
      width: 100%;
      height: 100%;
      overflow: hidden;
      overscroll-behavior: none;
      background-color: #0E0F1E;
      touch-action: manipulation;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      position: fixed;
      width: 100vw;
      height: 100vh;
    }

    #game-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      touch-action: manipulation;
    }

    /* Canvas optimization for iFrame */
    #game-container > canvas {
      display: block;
      touch-action: manipulation;
      image-rendering: high-quality;
    }

    /* Force canvas centering - critical fix */
    #game-container > canvas {
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      margin: 0 !important;
    }


    /* Prevent focus outline on mobile */
    *:focus, 
    *:active,
    *:active:focus {
      outline: none !important;
      box-shadow: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* Performance optimizations */
    #game-container, canvas {
      pointer-events: auto;
      will-change: transform;
    }
    
    /* Prevent text selection and context menus */
    body {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  </style>
</head>
<body>
  <div id="game-container"></div>
  <script type="module" src="index.ts"></script>
</body>
</html>
