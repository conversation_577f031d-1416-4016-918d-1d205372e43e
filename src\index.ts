import Phaser from 'phaser';
import PreloadScene from './scenes/PreloadScene';
import GameStartScene from './scenes/GameStartScene';
import GameScene from './scenes/GameScene';
import GameEndScene from './scenes/GameEndScene';
import GameConfig from './config/GameConfig';

declare global {
  interface TouchEvent {
    scale?: number;
  }
}

// Wait for DOM and ensure proper initialization
const initGame = () => {
  const container = document.getElementById('game-container');
  if (!container) {
    console.error('Game initialization failed: Game container not found');
    return;
  }

  const config: Phaser.Types.Core.GameConfig = {
    type: Phaser.CANVAS,
    width: GameConfig.GAME_WIDTH,
    height: GameConfig.GAME_HEIGHT,
    backgroundColor: GameConfig.BACKGROUND_COLOR,
    parent: 'game-container',
    scene: [PreloadScene, GameStartScene, GameScene, GameEndScene],
    
    // Physics configuration
    physics: {
      default: 'arcade',
      arcade: {
        gravity: { y: 0, x: 0 },
        debug: GameConfig.DEBUG_MODE
      }
    },
    
    // Performance settings
    fps: {
      target: 60,
      forceSetTimeOut: true,
      deltaHistory: 10,
      panicMax: 120
    },
    
    // Mobile-optimized scaling
    scale: {
      mode: Phaser.Scale.EXPAND,
      autoCenter: Phaser.Scale.CENTER_BOTH,
    },
    
    // Render optimizations
    render: {
      antialias: true,
      pixelArt: false,
      roundPixels: false,
      transparent: false,
      clearBeforeRender: true,
      preserveDrawingBuffer: false,
      failIfMajorPerformanceCaveat: false,
      powerPreference: 'high-performance'
    },
    
    // Audio configuration
    audio: {
      disableWebAudio: false,
      noAudio: false
    },
    
    // Touch input optimization
    input: {
      activePointers: GameConfig.MAX_ACTIVE_POINTERS,
      touch: {
        capture: false
      },
      mouse: {
        preventDefaultDown: true,
        preventDefaultUp: true,
        preventDefaultMove: true,
        preventDefaultWheel: false
      },
      keyboard: {
        capture: []
      },
      windowEvents: false
    },
    
    // DOM container
    dom: {
      createContainer: true
    },
    
    // Banner configuration
    banner: {
      hidePhaser: !GameConfig.DEBUG_MODE,
      text: GameConfig.DEBUG_MODE ? '#fff' : 'transparent',
      background: GameConfig.DEBUG_MODE ? ['#ff0000', '#ffff00', '#00ff00', '#00ffff', '#000000'] : ['transparent']
    }
  };

  try {
    const game = new Phaser.Game(config);

    // Mobile-specific optimizations
    setupMobileOptimizations(game);
    
    console.log('Finger Frenzy game initialized successfully');
    
    // Global error handlers
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error || event.message);
    });

    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
    
    // Handle visibility changes (for mobile app backgrounding)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('Game backgrounded - pausing audio and game loop');
        game.sound.pauseAll();
        game.loop.sleep();
      } else {
        console.log('Game foregrounded - resuming audio and game loop');
        game.sound.resumeAll();
        game.loop.wake();
      }
    });

  } catch (error) {
    console.error('Failed to initialize game:', error);
  }
};

// Mobile optimization helpers
const setupMobileOptimizations = (game: Phaser.Game) => {
  // Prevent zoom on double-tap
  let lastTouchEnd = 0;
  document.addEventListener('touchend', (event) => {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
      event.preventDefault();
    }
    lastTouchEnd = now;
  }, false);
  
  // Prevent context menu on long press
  document.addEventListener('contextmenu', (event) => {
    event.preventDefault();
  });
  
  // Handle orientation changes
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      game.scale.refresh();
    }, 100);
  });
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initGame);
} else {
  initGame();
}
