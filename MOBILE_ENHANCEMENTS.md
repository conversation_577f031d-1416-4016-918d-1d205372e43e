# Mobile Enhancements for Phaser 3 Games in Iframes

This guide provides comprehensive mobile optimizations for Phaser 3 games, with specific focus on iframe deployment scenarios.

## Table of Contents
1. [Touch Input Handling](#touch-input-handling)
2. [Iframe-specific Issues](#iframe-specific-issues)
3. [Mobile Browser Compatibility](#mobile-browser-compatibility)
4. [Performance Optimizations](#performance-optimizations)
5. [Audio on Mobile](#audio-on-mobile)
6. [Screen Orientation](#screen-orientation)

## Touch Input Handling

### Multi-touch Prevention
```javascript
// In your game config
const config = {
    input: {
        activePointers: 1, // Limit to single touch
    }
};

// Or dynamically in a scene
this.input.addPointer(1); // Total of 2 pointers (1 default + 1 added)
```

### Touch Event Normalization
```javascript
// Unified input handling for touch and mouse
create() {
    // This works for both touch and mouse
    this.input.on('pointerdown', this.handleInput, this);
    this.input.on('pointerup', this.handleRelease, this);
    this.input.on('pointermove', this.handleMove, this);
}

// Prevent touch from triggering mouse events
this.input.mouse.preventDefaultDown = true;
this.input.mouse.preventDefaultUp = true;
this.input.mouse.preventDefaultMove = true;
```

### Preventing Scroll/Zoom/Bounce
```css
/* In your CSS */
html, body {
    touch-action: none;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: auto;
    position: fixed;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

canvas {
    touch-action: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}
```

```javascript
// In JavaScript
document.addEventListener('touchmove', (e) => e.preventDefault(), { passive: false });
document.addEventListener('gesturestart', (e) => e.preventDefault());
document.addEventListener('gesturechange', (e) => e.preventDefault());

// Prevent double-tap zoom
let lastTouchEnd = 0;
document.addEventListener('touchend', (e) => {
    const now = Date.now();
    if (now - lastTouchEnd <= 300) {
        e.preventDefault();
    }
    lastTouchEnd = now;
}, false);
```

### Touch vs Mouse Events
```javascript
// Handle pointer slide-off prevention
create() {
    this.input.on('pointerdown', (pointer) => {
        // Store initial touch target
        this.touchTargets.set(pointer.id, pointer.downElement);
    });
    
    this.input.on('pointerup', (pointer) => {
        // Clean up
        this.touchTargets.delete(pointer.id);
    });
}

// Check if pointer is still on original target
isPointerOnOriginalTarget(pointer) {
    const originalTarget = this.touchTargets.get(pointer.id);
    return originalTarget === pointer.upElement;
}
```

## Iframe-specific Issues

### Focus Management
```javascript
// Ensure iframe maintains focus
window.addEventListener('blur', () => {
    window.focus();
});

// Request focus on game start
create() {
    window.focus();
    
    // For keyboard input in iframes
    this.input.keyboard.enabled = true;
    
    // Capture keyboard events at window level
    this.input.keyboard.captures = []; // Capture all keys
}
```

### Input Event Propagation
```javascript
// Prevent events from bubbling to parent
this.game.canvas.addEventListener('pointerdown', (e) => {
    e.stopPropagation();
});

// Allow specific keys to propagate (e.g., browser shortcuts)
this.input.keyboard.preventDefault = true;
this.input.keyboard.captures = [
    Phaser.Input.Keyboard.KeyCodes.SPACE,
    Phaser.Input.Keyboard.KeyCodes.UP,
    Phaser.Input.Keyboard.KeyCodes.DOWN,
    // Add other game keys
];
```

### Parent Window Communication
```javascript
// Send messages to parent
window.parent.postMessage({
    type: 'game-event',
    data: { score: this.score }
}, '*');

// Receive messages from parent
window.addEventListener('message', (event) => {
    if (event.data.type === 'pause-game') {
        this.scene.pause();
    }
});
```

### Fullscreen Handling
```html
<!-- In parent page -->
<iframe 
    src="game.html" 
    allowfullscreen="true"
    allow="fullscreen"
    webkitallowfullscreen="true"
    mozallowfullscreen="true">
</iframe>
```

```javascript
// In game
function enterFullscreen() {
    const elem = document.documentElement;
    if (elem.requestFullscreen) {
        elem.requestFullscreen();
    } else if (elem.webkitRequestFullscreen) {
        elem.webkitRequestFullscreen();
    }
}
```

### Viewport and Scaling
```javascript
// Handle iOS iframe viewport bug
if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
    // Force viewport update
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
        viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0';
    }
}

// Phaser scale config for iframes
const config = {
    scale: {
        mode: Phaser.Scale.FIT,
        parent: 'game-container',
        autoCenter: Phaser.Scale.CENTER_BOTH,
        width: 540,
        height: 960,
        expandParent: false, // Important for iframes
    }
};
```

## Mobile Browser Compatibility

### iOS Safari Fixes
```javascript
// Audio unlock for iOS
create() {
    // iOS requires user interaction to play audio
    this.input.once('pointerdown', () => {
        this.sound.unlock();
        
        // Create empty audio context to ensure it's ready
        if (this.sound.context) {
            this.sound.context.resume();
        }
    });
}

// Prevent iOS bounce scrolling
document.body.addEventListener('touchmove', (e) => {
    e.preventDefault();
}, { passive: false });

// Handle iOS viewport height changes (address bar)
function updateViewportHeight() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}
window.addEventListener('resize', updateViewportHeight);
updateViewportHeight();
```

### Android Chrome Soft Keyboard
```javascript
// Detect soft keyboard
let windowHeight = window.innerHeight;
window.addEventListener('resize', () => {
    const currentHeight = window.innerHeight;
    const keyboardHeight = windowHeight - currentHeight;
    
    if (keyboardHeight > 100) {
        // Keyboard is likely open
        this.onKeyboardOpen(keyboardHeight);
    } else {
        // Keyboard is likely closed
        this.onKeyboardClose();
    }
});

// Adjust game when keyboard opens
onKeyboardOpen(keyboardHeight) {
    // Move UI elements up
    this.uiContainer.y -= keyboardHeight / 2;
}
```

### WebView Detection
```javascript
// Detect if running in WebView
function isWebView() {
    const userAgent = navigator.userAgent;
    
    // Android WebView
    if (/wv/.test(userAgent)) return true;
    
    // iOS WebView
    if (/iPhone|iPod|iPad/.test(userAgent) && !/Safari/.test(userAgent)) {
        return true;
    }
    
    return false;
}

// Apply WebView-specific fixes
if (isWebView()) {
    // Disable certain features that don't work well in WebViews
    config.audio = { noAudio: true }; // If audio is problematic
}
```

### PWA Considerations
```javascript
// Service Worker for offline play
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js');
}

// Add to manifest.json
{
    "display": "fullscreen",
    "orientation": "portrait",
    "theme_color": "#000000",
    "background_color": "#000000"
}

// Handle PWA install prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    // Show install button
});
```

## Performance Optimizations

### Texture Atlas Usage
```javascript
// Load texture atlas instead of individual images
preload() {
    this.load.atlas('sprites', 'assets/sprites.png', 'assets/sprites.json');
}

// Use compressed textures for mobile
this.load.texture('background', {
    WEBGL: 'assets/bg.webp',
    Canvas: 'assets/bg.png'
});
```

### Draw Call Reduction
```javascript
// Use Phaser 3.60+ Mobile Pipeline
const config = {
    render: {
        pipeline: 'Mobile', // Optimized for mobile GPUs
        mipmapFilter: 'LINEAR_MIPMAP_LINEAR',
    }
};

// Batch similar sprites
create() {
    // Group sprites that use the same texture
    this.enemyGroup = this.add.group({
        defaultKey: 'enemy',
        maxSize: 50,
        createCallback: (enemy) => {
            enemy.setActive(false);
            enemy.setVisible(false);
        }
    });
}
```

### Mobile GPU Optimization
```javascript
// Reduce texture sizes for mobile
const textureQuality = this.sys.game.device.desktop ? 1 : 0.5;

// Power-of-two textures for better GPU performance
// Use 512x512, 1024x1024, etc.

// Disable expensive effects on low-end devices
if (this.sys.game.device.pixelRatio > 2) {
    // High DPI device, reduce effects
    this.particles.enabled = false;
}
```

### Battery Life Optimizations
```javascript
// Adaptive quality based on battery
if ('getBattery' in navigator) {
    navigator.getBattery().then((battery) => {
        if (battery.level < 0.3) {
            // Low battery mode
            this.game.config.fps.target = 30;
            this.reduceEffects();
        }
        
        battery.addEventListener('levelchange', () => {
            this.adjustQualityForBattery(battery.level);
        });
    });
}

// Pause when not visible
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        this.scene.pause();
    } else {
        this.scene.resume();
    }
});
```

## Audio on Mobile

### User Interaction Requirements
```javascript
create() {
    // Show play button overlay
    this.playButton = this.add.image(270, 480, 'playButton')
        .setInteractive()
        .once('pointerdown', () => {
            // Unlock audio on user interaction
            this.sound.unlock();
            
            // Resume audio context
            if (this.sound.context.state === 'suspended') {
                this.sound.context.resume();
            }
            
            // Start game
            this.playButton.destroy();
            this.startGame();
        });
}
```

### Audio Context Management
```javascript
// Handle audio context suspension
create() {
    // Check audio context state periodically
    this.time.addEvent({
        delay: 1000,
        loop: true,
        callback: () => {
            if (this.sound.context && this.sound.context.state === 'suspended') {
                this.sound.context.resume();
            }
        }
    });
}

// Handle iOS audio interruptions
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && this.sound.context) {
        this.sound.context.resume();
    }
});
```

### Format Compatibility
```javascript
// Load multiple formats with fallback
preload() {
    // Order matters - list preferred format first
    this.load.audio('bgMusic', [
        'assets/audio/music.m4a',  // iOS preferred
        'assets/audio/music.ogg',  // Android preferred
        'assets/audio/music.mp3'   // Universal fallback
    ]);
}

// Check format support
const canPlayOgg = this.sound.context.createMediaElementSource !== undefined;
```

## Screen Orientation

### Orientation Change Detection
```javascript
create() {
    // Listen for orientation changes
    this.scale.on('orientationchange', (orientation) => {
        if (orientation === Phaser.Scale.PORTRAIT) {
            this.handlePortrait();
        } else {
            this.handleLandscape();
        }
    });
}

// Check initial orientation
checkOrientation() {
    const { width, height } = this.cameras.main;
    return width > height ? 'landscape' : 'portrait';
}
```

### Soft-lock Orientation
```javascript
// Show "please rotate" screen
create() {
    this.rotateScreen = this.add.container(270, 480);
    
    const bg = this.add.rectangle(0, 0, 540, 960, 0x000000, 0.9);
    const icon = this.add.image(0, 0, 'rotateIcon');
    const text = this.add.text(0, 100, 'Please rotate your device', {
        fontSize: '24px',
        color: '#ffffff',
        align: 'center'
    }).setOrigin(0.5);
    
    this.rotateScreen.add([bg, icon, text]);
    this.rotateScreen.setDepth(1000);
    this.rotateScreen.setVisible(false);
    
    // Check orientation
    this.scale.on('orientationchange', () => {
        this.updateOrientationLock();
    });
    
    this.updateOrientationLock();
}

updateOrientationLock() {
    const wrongOrientation = this.scale.orientation !== Phaser.Scale.PORTRAIT;
    this.rotateScreen.setVisible(wrongOrientation);
    
    if (wrongOrientation) {
        this.scene.pause();
    } else {
        this.scene.resume();
    }
}
```

### Responsive Design
```javascript
// Dynamic layout based on orientation
handleOrientationChange() {
    const { width, height } = this.cameras.main;
    const isPortrait = height > width;
    
    if (isPortrait) {
        // Portrait layout
        this.uiTop.y = height * 0.1;
        this.gameArea.setScale(1);
    } else {
        // Landscape layout
        this.uiTop.y = height * 0.05;
        this.gameArea.setScale(0.8);
    }
    
    // Reposition all UI elements
    this.repositionUI();
}
```

## Implementation Checklist

- [ ] Add touch input normalization
- [ ] Implement pointer tracking for held touches
- [ ] Add viewport meta tags for mobile
- [ ] Configure scale mode for responsive design
- [ ] Add audio unlock on user interaction
- [ ] Implement orientation handling
- [ ] Add WebView detection and fixes
- [ ] Optimize textures and reduce draw calls
- [ ] Add battery level awareness
- [ ] Test on iOS Safari, Android Chrome, and WebViews
- [ ] Add CSS to prevent scrolling and zooming
- [ ] Implement parent window communication
- [ ] Add proper iframe attributes
- [ ] Handle soft keyboard on Android
- [ ] Add "Add to Home Screen" PWA support