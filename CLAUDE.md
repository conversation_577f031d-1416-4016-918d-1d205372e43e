# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm start` - Start dev server at http://localhost:9000
- `npm run build` - Build production bundle to dist/

### Testing
No test framework is currently configured.

## Architecture Overview

This is a Phaser 3 mobile game implementing a fast-paced block-tapping game. The architecture follows a simplified scene-based pattern without complex manager systems.

### Scene Flow
1. **PreloadScene** → Loads all game assets
2. **GameStartScene** → Title screen with play button
3. **GameScene** → Main 4x4 grid gameplay (30 seconds)
4. **GameEndScene** → Shows score and back to lobby button

### Core Game Mechanics

**Scoring System**: Points awarded based on reaction time:
- < 500ms: 5 points
- < 1000ms: 4 points
- < 1500ms: 3 points
- < 2000ms: 2 points
- ≥ 2000ms: 1 point
- Wrong tap (inactive block): -5 points

**Grid System**: The game uses a 4x4 grid of `GridBlock` objects:
- 3 blocks are active (colored) at any time
- Tapping an active block deactivates it and activates a new random block
- Blocks have visual states: inactive (gray), active (colored), wrong tap feedback (red flash)

### Touch Handling

The game implements pointer tracking to prevent held-touch issues:
- Maps pointer IDs to their initial target blocks
- Prevents a held touch from registering on different blocks
- Critical for mobile gameplay fairness

### Platform Integration

The game integrates with TicTaps platform via `TicTapsConnector`:
- Communicates game start/end events
- Submits final scores
- Handles lobby return navigation
- Uses postMessage API for iframe communication

### Asset Pipeline

Images are automatically optimized during webpack build:
- PNG/JPEG compression via Sharp
- SVG optimization via SVGO
- All assets copied to dist/assets/

### Configuration

All game settings are centralized in `src/config/GameConfig.ts`:
- Screen dimensions (540x960)
- Grid configuration (4x4, 3 active blocks)
- Scoring thresholds and penalties
- Animation durations
- Color schemes and gradients

### Debug Mode

Debug display available in development mode through GameConfig.DEBUG_MODE setting.