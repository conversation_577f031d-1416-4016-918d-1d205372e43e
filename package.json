{"name": "finger-frenzy-phaser", "version": "1.0.0", "description": "Finger Frenzy game ported to Phaser 3 with TypeScript", "main": "src/index.ts", "scripts": {"start": "webpack serve --mode development", "build": "webpack --mode production"}, "keywords": ["phaser", "game", "tictaps", "typescript", "finger-frenzy"], "author": "", "license": "ISC", "dependencies": {"phaser": "^3.90.0"}, "devDependencies": {"@types/node": "^20.0.0", "copy-webpack-plugin": "^11.0.0", "html-webpack-plugin": "^5.5.0", "image-minimizer-webpack-plugin": "^4.1.3", "sharp": "^0.34.2", "svgo": "^3.3.2", "ts-loader": "^9.4.0", "typescript": "^5.0.0", "webpack": "^5.76.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}