/**
 * TicTapsConnector - <PERSON>les communication with TicTaps platform
 * Equivalent to the TicTaps class in the Unity version
 */
export default class TicTapsConnector {
  private isWebGL: boolean;

  constructor() {
    this.isWebGL = this.checkIfWebGL();
  }

  /**
   * Check if running in AdvancedDebugDisplay.ts browser environment and embedded
   */
  private checkIfWebGL(): boolean {
    return (typeof window !== 'undefined' && window.parent && window.parent !== window);
  }

  /**
   * Notify parent window that the game is ready
   * Equivalent to TicTaps.Instance.NotifyGameReady()
   */
  notifyGameReady(): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameReady' }, '*');
      console.log('Game ready notification sent to parent');
    }
  }

  /**
   * Send score to parent window
   * Equivalent to TicTaps.Instance.SendScore()
   */
  sendScore(score: number): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameScore', score: score }, '*');
      console.log('Score sent to parent:', score);
    }
  }

  /**
   * Notify parent window that the game has quit
   * Equivalent to TicTaps.Instance.NotifyGameQuit()
   */
  notifyGameQuit(): void {
    if (this.isWebGL && window.parent && typeof window.parent.postMessage === 'function') {
      window.parent.postMessage({ type: 'gameQuit' }, '*');
      console.log('Game quit notification sent to parent');
    }
  }

  /**
   * Check if running in WebGL environment
   */
  isRunningInWebGL(): boolean {
    return this.isWebGL;
  }
}
